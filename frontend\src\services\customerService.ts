import api from './api'
import { Customer, CustomerFormData } from '../types'

export const customerService = {
  async getCustomers(params?: {
    skip?: number
    limit?: number
    search?: string
    connection_type?: string
    status?: string
    plan_id?: number
  }): Promise<Customer[]> {
    const response = await api.get('/api/customers', { params })
    return response.data
  },

  async getCustomer(id: number): Promise<Customer> {
    const response = await api.get(`/api/customers/${id}`)
    return response.data
  },

  async createCustomer(data: CustomerFormData): Promise<Customer> {
    const response = await api.post('/api/customers', data)
    return response.data
  },

  async updateCustomer(id: number, data: Partial<CustomerFormData>): Promise<Customer> {
    const response = await api.put(`/api/customers/${id}`, data)
    return response.data
  },

  async deleteCustomer(id: number): Promise<void> {
    await api.delete(`/api/customers/${id}`)
  },
}
