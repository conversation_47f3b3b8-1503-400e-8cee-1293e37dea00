import React from 'react'
import { Box, Typography, Button, Paper } from '@mui/material'
import { Add } from '@mui/icons-material'

const Users: React.FC = () => {
  return (
    <Box sx={{ flexGrow: 1 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" gutterBottom fontWeight="bold">
          Users
        </Typography>
        <Button variant="contained" startIcon={<Add />}>
          Add User
        </Button>
      </Box>

      <Paper sx={{ p: 4, textAlign: 'center' }}>
        <Typography variant="h6" color="text.secondary">
          User management coming soon...
        </Typography>
      </Paper>
    </Box>
  )
}

export default Users
