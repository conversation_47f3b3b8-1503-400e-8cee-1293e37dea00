version: '3.8'

services:
  # PostgreSQL Database
  db:
    image: postgres:15
    container_name: cable_db
    environment:
      POSTGRES_DB: cable_management
      POSTGRES_USER: cable_user
      POSTGRES_PASSWORD: cable_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - cable_network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: cable_backend
    environment:
      DATABASE_URL: **********************************************/cable_management
      SECRET_KEY: your-super-secret-key-change-this-in-production
      ALGORITHM: HS256
      ACCESS_TOKEN_EXPIRE_MINUTES: 30
      SMTP_SERVER: smtp.gmail.com
      SMTP_PORT: 587
      SMTP_USERNAME: <EMAIL>
      SMTP_PASSWORD: your-app-password
      SMTP_USE_TLS: true
      APP_NAME: Cable Operator Management System
      APP_VERSION: 1.0.0
      DEBUG: true
      ALLOWED_ORIGINS: http://localhost:3000,http://localhost:5173
      MAX_FILE_SIZE: 10485760
      UPLOAD_DIR: uploads
    volumes:
      - ./backend:/app
      - backend_uploads:/app/uploads
    ports:
      - "8000:8000"
    depends_on:
      - db
    networks:
      - cable_network
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: cable_frontend
    environment:
      VITE_API_URL: http://localhost:8000
      VITE_APP_NAME: Cable Operator Management System
      VITE_APP_VERSION: 1.0.0
    volumes:
      - ./frontend:/app
      - /app/node_modules
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - cable_network
    command: npm run dev -- --host 0.0.0.0

volumes:
  postgres_data:
  backend_uploads:

networks:
  cable_network:
    driver: bridge
