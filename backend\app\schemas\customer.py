from pydantic import BaseModel, EmailStr
from typing import Optional
from datetime import datetime
from app.models.customer import ConnectionType, CustomerStatus

class CustomerBase(BaseModel):
    name: str
    email: Optional[EmailStr] = None
    phone: str
    address: str
    connection_type: ConnectionType
    connection_date: datetime
    status: CustomerStatus = CustomerStatus.ACTIVE
    plan_id: Optional[int] = None
    notes: Optional[str] = None
    installation_address: Optional[str] = None

class CustomerCreate(CustomerBase):
    pass

class CustomerUpdate(BaseModel):
    name: Optional[str] = None
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    address: Optional[str] = None
    connection_type: Optional[ConnectionType] = None
    connection_date: Optional[datetime] = None
    status: Optional[CustomerStatus] = None
    plan_id: Optional[int] = None
    notes: Optional[str] = None
    installation_address: Optional[str] = None

class CustomerResponse(CustomerBase):
    id: int
    customer_id: str
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    # Include plan details if assigned
    plan: Optional[dict] = None

    class Config:
        from_attributes = True

class CustomerSearch(BaseModel):
    query: Optional[str] = None
    connection_type: Optional[ConnectionType] = None
    status: Optional[CustomerStatus] = None
    plan_id: Optional[int] = None
    page: int = 1
    limit: int = 10
