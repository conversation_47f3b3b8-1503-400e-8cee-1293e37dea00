# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/cable_management

# Security
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Email Configuration
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_USE_TLS=true

# Application Settings
APP_NAME=Cable Operator Management System
APP_VERSION=1.0.0
DEBUG=true

# CORS Settings
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173

# File Upload Settings
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_DIR=uploads
