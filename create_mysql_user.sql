-- Cable Management System - MySQL User Setup
-- Run these commands in MySQL Workbench

-- 1. Create the cable_user
CREATE USER 'cable_user'@'localhost' IDENTIFIED BY 'cable123';

-- 2. Grant all privileges on the cable_software database
GRANT ALL PRIVILEGES ON cable_software.* TO 'cable_user'@'localhost';

-- 3. Grant additional privileges if needed
GRANT CREATE, ALTER, DROP, INSERT, UPDATE, DELETE, SELECT, REFERENCES, RELOAD on *.* TO 'cable_user'@'localhost';

-- 4. Apply the changes
FLUSH PRIVILEGES;

-- 5. Verify the user was created
SELECT User, Host FROM mysql.user WHERE User = 'cable_user';

-- 6. Check the grants
SHOW GRANTS FOR 'cable_user'@'localhost';

-- 7. Test the database exists
SHOW DATABASES LIKE 'cable_software';
