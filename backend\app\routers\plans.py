from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, or_
from typing import List, Optional
from decimal import Decimal
from app.database import get_db
from app.models.plan import Plan, PlanType
from app.models.customer import Customer
from app.models.user import User
from app.schemas.plan import PlanCreate, PlanUpdate, PlanResponse, PlanSearch
from app.core.security import get_current_active_user

router = APIRouter()

@router.get("/", response_model=List[PlanResponse])
async def get_plans(
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    search: Optional[str] = None,
    plan_type: Optional[PlanType] = None,
    is_active: Optional[bool] = None,
    min_price: Optional[Decimal] = None,
    max_price: Optional[Decimal] = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get list of plans with filtering and search."""
    query = db.query(Plan)
    
    # Search functionality
    if search:
        search_filter = or_(
            Plan.name.ilike(f"%{search}%"),
            Plan.description.ilike(f"%{search}%")
        )
        query = query.filter(search_filter)
    
    # Filters
    if plan_type:
        query = query.filter(Plan.plan_type == plan_type)
    
    if is_active is not None:
        query = query.filter(Plan.is_active == is_active)
    
    if min_price is not None:
        query = query.filter(Plan.price >= min_price)
    
    if max_price is not None:
        query = query.filter(Plan.price <= max_price)
    
    plans = query.offset(skip).limit(limit).all()
    
    # Add customer count for each plan
    result = []
    for plan in plans:
        plan_dict = PlanResponse.from_orm(plan).dict()
        customer_count = db.query(Customer).filter(Customer.plan_id == plan.id).count()
        plan_dict["customer_count"] = customer_count
        result.append(plan_dict)
    
    return result

@router.post("/", response_model=PlanResponse)
async def create_plan(
    plan_data: PlanCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create a new plan."""
    # Check if plan name already exists
    existing_plan = db.query(Plan).filter(Plan.name == plan_data.name).first()
    if existing_plan:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Plan with this name already exists"
        )
    
    # Create plan
    db_plan = Plan(**plan_data.dict())
    db.add(db_plan)
    db.commit()
    db.refresh(db_plan)
    
    plan_response = PlanResponse.from_orm(db_plan)
    plan_response.customer_count = 0
    
    return plan_response

@router.get("/{plan_id}", response_model=PlanResponse)
async def get_plan(
    plan_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get plan by ID."""
    plan = db.query(Plan).filter(Plan.id == plan_id).first()
    if not plan:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Plan not found"
        )
    
    plan_response = PlanResponse.from_orm(plan)
    customer_count = db.query(Customer).filter(Customer.plan_id == plan.id).count()
    plan_response.customer_count = customer_count
    
    return plan_response

@router.put("/{plan_id}", response_model=PlanResponse)
async def update_plan(
    plan_id: int,
    plan_data: PlanUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update plan."""
    plan = db.query(Plan).filter(Plan.id == plan_id).first()
    if not plan:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Plan not found"
        )
    
    # Check if plan name already exists (excluding current plan)
    if plan_data.name:
        existing_plan = db.query(Plan).filter(
            Plan.name == plan_data.name,
            Plan.id != plan_id
        ).first()
        if existing_plan:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Plan with this name already exists"
            )
    
    # Update plan fields
    update_data = plan_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(plan, field, value)
    
    db.commit()
    db.refresh(plan)
    
    plan_response = PlanResponse.from_orm(plan)
    customer_count = db.query(Customer).filter(Customer.plan_id == plan.id).count()
    plan_response.customer_count = customer_count
    
    return plan_response

@router.delete("/{plan_id}")
async def delete_plan(
    plan_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete plan."""
    plan = db.query(Plan).filter(Plan.id == plan_id).first()
    if not plan:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Plan not found"
        )
    
    # Check if plan is assigned to any customers
    customer_count = db.query(Customer).filter(Customer.plan_id == plan_id).count()
    if customer_count > 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Cannot delete plan. It is assigned to {customer_count} customer(s)"
        )
    
    db.delete(plan)
    db.commit()
    
    return {"message": "Plan deleted successfully"}

@router.get("/{plan_id}/customers")
async def get_plan_customers(
    plan_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get customers assigned to a specific plan."""
    plan = db.query(Plan).filter(Plan.id == plan_id).first()
    if not plan:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Plan not found"
        )
    
    customers = db.query(Customer).filter(Customer.plan_id == plan_id).offset(skip).limit(limit).all()
    
    return {
        "plan": {
            "id": plan.id,
            "name": plan.name,
            "plan_type": plan.plan_type,
            "price": float(plan.price)
        },
        "customers": [
            {
                "id": customer.id,
                "customer_id": customer.customer_id,
                "name": customer.name,
                "phone": customer.phone,
                "status": customer.status
            }
            for customer in customers
        ],
        "total_customers": db.query(Customer).filter(Customer.plan_id == plan_id).count()
    }
