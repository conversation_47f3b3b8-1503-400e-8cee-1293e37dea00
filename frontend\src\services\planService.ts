import api from './api'
import { Plan, PlanFormData } from '../types'

export const planService = {
  async getPlans(params?: {
    skip?: number
    limit?: number
    search?: string
    plan_type?: string
    is_active?: boolean
    min_price?: number
    max_price?: number
  }): Promise<Plan[]> {
    const response = await api.get('/api/plans', { params })
    return response.data
  },

  async getPlan(id: number): Promise<Plan> {
    const response = await api.get(`/api/plans/${id}`)
    return response.data
  },

  async createPlan(data: PlanFormData): Promise<Plan> {
    const response = await api.post('/api/plans', data)
    return response.data
  },

  async updatePlan(id: number, data: Partial<PlanFormData>): Promise<Plan> {
    const response = await api.put(`/api/plans/${id}`, data)
    return response.data
  },

  async deletePlan(id: number): Promise<void> {
    await api.delete(`/api/plans/${id}`)
  },

  async getPlanCustomers(id: number, params?: { skip?: number; limit?: number }) {
    const response = await api.get(`/api/plans/${id}/customers`, { params })
    return response.data
  },
}
