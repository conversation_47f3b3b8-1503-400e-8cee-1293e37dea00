import React from 'react'
import {
  Grid,
  Paper,
  Typography,
  Box,
  Card,
  CardContent,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Chip,
} from '@mui/material'
import {
  People,
  Payment,
  TrendingUp,
  Warning,
  PersonAdd,
  AttachMoney,
} from '@mui/icons-material'
import { useQuery } from 'react-query'
import { dashboardService } from '../../services/dashboardService'
import LoadingSpinner from '../../components/UI/LoadingSpinner'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts'

const Dashboard: React.FC = () => {
  const { data: stats, isLoading: statsLoading } = useQuery(
    'dashboard-stats',
    dashboardService.getStats
  )

  const { data: revenueChart, isLoading: revenueLoading } = useQuery(
    'revenue-chart',
    () => dashboardService.getRevenueChart(6)
  )

  const { data: activities, isLoading: activitiesLoading } = useQuery(
    'recent-activities',
    () => dashboardService.getRecentActivities(5)
  )

  if (statsLoading) {
    return <LoadingSpinner size="large" className="min-h-screen" />
  }

  const COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6']

  const pieData = stats?.plans.distribution.map((plan, index) => ({
    name: plan.name,
    value: plan.customer_count,
    color: COLORS[index % COLORS.length],
  })) || []

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Typography variant="h4" gutterBottom fontWeight="bold">
        Dashboard
      </Typography>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                  <People />
                </Avatar>
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    {stats?.customers.total || 0}
                  </Typography>
                  <Typography color="text.secondary">Total Customers</Typography>
                </Box>
              </Box>
              <Typography variant="body2" color="success.main">
                {stats?.customers.active || 0} active
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}>
                  <AttachMoney />
                </Avatar>
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    ${stats?.payments.monthly_revenue.toFixed(2) || '0.00'}
                  </Typography>
                  <Typography color="text.secondary">Monthly Revenue</Typography>
                </Box>
              </Box>
              <Typography variant="body2" color="text.secondary">
                Total: ${stats?.payments.total_revenue.toFixed(2) || '0.00'}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'warning.main', mr: 2 }}>
                  <Warning />
                </Avatar>
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    {stats?.payments.overdue || 0}
                  </Typography>
                  <Typography color="text.secondary">Overdue Payments</Typography>
                </Box>
              </Box>
              <Typography variant="body2" color="warning.main">
                {stats?.payments.pending || 0} pending
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'info.main', mr: 2 }}>
                  <PersonAdd />
                </Avatar>
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    {stats?.customers.new_this_month || 0}
                  </Typography>
                  <Typography color="text.secondary">New This Month</Typography>
                </Box>
              </Box>
              <Typography variant="body2" color="info.main">
                +{((stats?.customers.new_this_month || 0) / (stats?.customers.total || 1) * 100).toFixed(1)}%
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Revenue Chart */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3, height: 400 }}>
            <Typography variant="h6" gutterBottom>
              Revenue Trend (Last 6 Months)
            </Typography>
            {revenueLoading ? (
              <LoadingSpinner />
            ) : (
              <ResponsiveContainer width="100%" height="90%">
                <LineChart data={revenueChart?.chart_data || []}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip formatter={(value) => [`$${value}`, 'Revenue']} />
                  <Line
                    type="monotone"
                    dataKey="revenue"
                    stroke="#3b82f6"
                    strokeWidth={3}
                    dot={{ fill: '#3b82f6', strokeWidth: 2, r: 6 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            )}
          </Paper>
        </Grid>

        {/* Plan Distribution */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, height: 400 }}>
            <Typography variant="h6" gutterBottom>
              Plan Distribution
            </Typography>
            <ResponsiveContainer width="100%" height="90%">
              <PieChart>
                <Pie
                  data={pieData}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, value }) => `${name}: ${value}`}
                >
                  {pieData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Recent Activities */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Recent Activities
            </Typography>
            {activitiesLoading ? (
              <LoadingSpinner />
            ) : (
              <List>
                {activities?.activities?.slice(0, 5).map((activity: any, index: number) => (
                  <ListItem key={index} divider={index < 4}>
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: activity.type === 'payment' ? 'success.main' : 'primary.main' }}>
                        {activity.type === 'payment' ? <Payment /> : <PersonAdd />}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={activity.description}
                      secondary={new Date(activity.date).toLocaleDateString()}
                    />
                    {activity.amount && (
                      <Chip
                        label={`$${activity.amount.toFixed(2)}`}
                        color="success"
                        size="small"
                      />
                    )}
                  </ListItem>
                ))}
              </List>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Box>
  )
}

export default Dashboard
