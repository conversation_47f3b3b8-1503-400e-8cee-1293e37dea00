# Minimal FastAPI setup for testing without compilation issues
fastapi==0.104.1
uvicorn==0.24.0

# Database (using SQLite for testing)
sqlalchemy==2.0.23

# Authentication (simplified)
python-jose==3.3.0
passlib==1.7.4

# Form handling
python-multipart==0.0.6

# Configuration
python-dotenv==1.0.0

# Templates
jinja2==3.1.2

# File handling
aiofiles==23.2.1

# Date handling
python-dateutil==2.8.2

# Note: We're using older/simpler versions to avoid compilation issues
# For production, you would want the full versions with all features
