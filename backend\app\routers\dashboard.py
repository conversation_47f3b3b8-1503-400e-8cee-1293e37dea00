from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, extract, and_
from typing import Optional
from datetime import datetime, timedelta
from decimal import Decimal
from app.database import get_db
from app.models.customer import Customer, CustomerStatus
from app.models.payment import Payment, PaymentStatus
from app.models.plan import Plan, PlanType
from app.models.invoice import Invoice, InvoiceStatus
from app.models.user import User
from app.core.security import get_current_active_user

router = APIRouter()

@router.get("/stats")
async def get_dashboard_stats(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get dashboard statistics."""
    
    # Customer statistics
    total_customers = db.query(Customer).count()
    active_customers = db.query(Customer).filter(Customer.status == CustomerStatus.ACTIVE).count()
    inactive_customers = db.query(Customer).filter(Customer.status == CustomerStatus.INACTIVE).count()
    suspended_customers = db.query(Customer).filter(Customer.status == CustomerStatus.SUSPENDED).count()
    
    # Payment statistics
    current_month = datetime.now().month
    current_year = datetime.now().year
    
    # Monthly revenue
    monthly_revenue = db.query(func.sum(Payment.amount)).filter(
        and_(
            Payment.status == PaymentStatus.PAID,
            Payment.billing_month == current_month,
            Payment.billing_year == current_year
        )
    ).scalar() or Decimal('0')
    
    # Overdue payments
    overdue_payments = db.query(Payment).filter(
        and_(
            Payment.status == PaymentStatus.OVERDUE,
            Payment.due_date < datetime.now()
        )
    ).count()
    
    # Pending payments
    pending_payments = db.query(Payment).filter(Payment.status == PaymentStatus.PENDING).count()
    
    # Total revenue (all time)
    total_revenue = db.query(func.sum(Payment.amount)).filter(
        Payment.status == PaymentStatus.PAID
    ).scalar() or Decimal('0')
    
    # Plan distribution
    plan_distribution = db.query(
        Plan.name,
        Plan.plan_type,
        func.count(Customer.id).label('customer_count')
    ).outerjoin(Customer, Plan.id == Customer.plan_id).group_by(Plan.id, Plan.name, Plan.plan_type).all()
    
    # Recent customers (last 30 days)
    thirty_days_ago = datetime.now() - timedelta(days=30)
    new_customers = db.query(Customer).filter(Customer.created_at >= thirty_days_ago).count()
    
    return {
        "customers": {
            "total": total_customers,
            "active": active_customers,
            "inactive": inactive_customers,
            "suspended": suspended_customers,
            "new_this_month": new_customers
        },
        "payments": {
            "monthly_revenue": float(monthly_revenue),
            "total_revenue": float(total_revenue),
            "overdue": overdue_payments,
            "pending": pending_payments
        },
        "plans": {
            "distribution": [
                {
                    "name": plan.name,
                    "type": plan.plan_type,
                    "customer_count": plan.customer_count
                }
                for plan in plan_distribution
            ]
        }
    }

@router.get("/revenue-chart")
async def get_revenue_chart(
    months: int = Query(12, ge=1, le=24),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get revenue chart data for the last N months."""
    
    # Calculate date range
    end_date = datetime.now()
    start_date = end_date - timedelta(days=months * 30)
    
    # Get monthly revenue data
    revenue_data = db.query(
        extract('year', Payment.created_at).label('year'),
        extract('month', Payment.created_at).label('month'),
        func.sum(Payment.amount).label('revenue')
    ).filter(
        and_(
            Payment.status == PaymentStatus.PAID,
            Payment.created_at >= start_date
        )
    ).group_by(
        extract('year', Payment.created_at),
        extract('month', Payment.created_at)
    ).order_by(
        extract('year', Payment.created_at),
        extract('month', Payment.created_at)
    ).all()
    
    # Format data for chart
    chart_data = []
    for data in revenue_data:
        month_name = datetime(int(data.year), int(data.month), 1).strftime('%B %Y')
        chart_data.append({
            "month": month_name,
            "revenue": float(data.revenue or 0)
        })
    
    return {"chart_data": chart_data}

@router.get("/customer-growth")
async def get_customer_growth(
    months: int = Query(12, ge=1, le=24),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get customer growth chart data."""
    
    # Calculate date range
    end_date = datetime.now()
    start_date = end_date - timedelta(days=months * 30)
    
    # Get monthly customer registration data
    customer_data = db.query(
        extract('year', Customer.created_at).label('year'),
        extract('month', Customer.created_at).label('month'),
        func.count(Customer.id).label('new_customers')
    ).filter(
        Customer.created_at >= start_date
    ).group_by(
        extract('year', Customer.created_at),
        extract('month', Customer.created_at)
    ).order_by(
        extract('year', Customer.created_at),
        extract('month', Customer.created_at)
    ).all()
    
    # Format data for chart
    chart_data = []
    total_customers = 0
    
    for data in customer_data:
        month_name = datetime(int(data.year), int(data.month), 1).strftime('%B %Y')
        total_customers += data.new_customers
        chart_data.append({
            "month": month_name,
            "new_customers": data.new_customers,
            "total_customers": total_customers
        })
    
    return {"chart_data": chart_data}

@router.get("/recent-activities")
async def get_recent_activities(
    limit: int = Query(10, ge=1, le=50),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get recent activities (payments, new customers, etc.)."""
    
    activities = []
    
    # Recent payments
    recent_payments = db.query(Payment).filter(
        Payment.status == PaymentStatus.PAID
    ).order_by(Payment.payment_date.desc()).limit(limit // 2).all()
    
    for payment in recent_payments:
        activities.append({
            "type": "payment",
            "description": f"Payment received from {payment.customer.name}",
            "amount": float(payment.amount),
            "date": payment.payment_date,
            "customer": payment.customer.name
        })
    
    # Recent customers
    recent_customers = db.query(Customer).order_by(Customer.created_at.desc()).limit(limit // 2).all()
    
    for customer in recent_customers:
        activities.append({
            "type": "customer",
            "description": f"New customer registered: {customer.name}",
            "date": customer.created_at,
            "customer": customer.name,
            "connection_type": customer.connection_type
        })
    
    # Sort all activities by date
    activities.sort(key=lambda x: x["date"], reverse=True)
    
    return {"activities": activities[:limit]}

@router.get("/payment-status-summary")
async def get_payment_status_summary(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get payment status summary for current month."""
    
    current_month = datetime.now().month
    current_year = datetime.now().year
    
    # Get payment status counts for current month
    payment_summary = db.query(
        Payment.status,
        func.count(Payment.id).label('count'),
        func.sum(Payment.amount).label('total_amount')
    ).filter(
        and_(
            Payment.billing_month == current_month,
            Payment.billing_year == current_year
        )
    ).group_by(Payment.status).all()
    
    summary = {}
    for status, count, total_amount in payment_summary:
        summary[status] = {
            "count": count,
            "total_amount": float(total_amount or 0)
        }
    
    return {"payment_summary": summary}

@router.get("/top-plans")
async def get_top_plans(
    limit: int = Query(5, ge=1, le=10),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get top plans by customer count."""
    
    top_plans = db.query(
        Plan.id,
        Plan.name,
        Plan.plan_type,
        Plan.price,
        func.count(Customer.id).label('customer_count')
    ).outerjoin(Customer, Plan.id == Customer.plan_id).group_by(
        Plan.id, Plan.name, Plan.plan_type, Plan.price
    ).order_by(func.count(Customer.id).desc()).limit(limit).all()
    
    plans_data = []
    for plan in top_plans:
        plans_data.append({
            "id": plan.id,
            "name": plan.name,
            "type": plan.plan_type,
            "price": float(plan.price),
            "customer_count": plan.customer_count
        })
    
    return {"top_plans": plans_data}
