import React from 'react'
import {
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Box,
  Typography,
  Divider,
  Collapse,
} from '@mui/material'
import {
  Dashboard,
  People,
  Payment,
  Receipt,
  Settings,
  Business,
  Group,
  ExpandLess,
  ExpandMore,
  Cable,
} from '@mui/icons-material'
import { useNavigate, useLocation } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext'

interface SidebarProps {
  open: boolean
  onClose: () => void
  isMobile: boolean
}

const Sidebar: React.FC<SidebarProps> = ({ open, onClose, isMobile }) => {
  const navigate = useNavigate()
  const location = useLocation()
  const { user } = useAuth()
  const [managementOpen, setManagementOpen] = React.useState(true)

  const menuItems = [
    {
      text: 'Dashboard',
      icon: <Dashboard />,
      path: '/dashboard',
    },
    {
      text: 'Management',
      icon: <Business />,
      isSection: true,
      children: [
        {
          text: 'Customers',
          icon: <People />,
          path: '/customers',
        },
        {
          text: 'Plans',
          icon: <Cable />,
          path: '/plans',
        },
        {
          text: 'Payments',
          icon: <Payment />,
          path: '/payments',
        },
        {
          text: 'Invoices',
          icon: <Receipt />,
          path: '/invoices',
        },
      ],
    },
  ]

  // Add admin/owner only items
  if (user?.role === 'owner' || user?.role === 'admin') {
    menuItems.push({
      text: 'Users',
      icon: <Group />,
      path: '/users',
    })
  }

  menuItems.push({
    text: 'Settings',
    icon: <Settings />,
    path: '/settings',
  })

  const handleNavigation = (path: string) => {
    navigate(path)
    if (isMobile) {
      onClose()
    }
  }

  const isActive = (path: string) => location.pathname === path

  const drawerWidth = open ? 280 : 80

  const drawerContent = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <Box sx={{ p: 2, mt: 8 }}>
        <Typography variant="h6" noWrap>
          {open ? 'Cable CRM' : 'CC'}
        </Typography>
      </Box>
      <Divider />
      
      <List sx={{ flexGrow: 1, px: 1 }}>
        {menuItems.map((item) => {
          if (item.isSection) {
            return (
              <React.Fragment key={item.text}>
                <ListItem disablePadding>
                  <ListItemButton
                    onClick={() => setManagementOpen(!managementOpen)}
                    sx={{
                      minHeight: 48,
                      justifyContent: open ? 'initial' : 'center',
                      px: 2.5,
                      borderRadius: 1,
                      mb: 0.5,
                    }}
                  >
                    <ListItemIcon
                      sx={{
                        minWidth: 0,
                        mr: open ? 3 : 'auto',
                        justifyContent: 'center',
                      }}
                    >
                      {item.icon}
                    </ListItemIcon>
                    {open && (
                      <>
                        <ListItemText primary={item.text} />
                        {managementOpen ? <ExpandLess /> : <ExpandMore />}
                      </>
                    )}
                  </ListItemButton>
                </ListItem>
                
                {open && (
                  <Collapse in={managementOpen} timeout="auto" unmountOnExit>
                    <List component="div" disablePadding>
                      {item.children?.map((child) => (
                        <ListItem key={child.text} disablePadding>
                          <ListItemButton
                            onClick={() => handleNavigation(child.path)}
                            sx={{
                              minHeight: 48,
                              pl: 4,
                              borderRadius: 1,
                              mb: 0.5,
                              backgroundColor: isActive(child.path) 
                                ? 'primary.main' 
                                : 'transparent',
                              color: isActive(child.path) 
                                ? 'primary.contrastText' 
                                : 'text.primary',
                              '&:hover': {
                                backgroundColor: isActive(child.path) 
                                  ? 'primary.dark' 
                                  : 'action.hover',
                              },
                            }}
                          >
                            <ListItemIcon
                              sx={{
                                minWidth: 0,
                                mr: 3,
                                justifyContent: 'center',
                                color: isActive(child.path) 
                                  ? 'primary.contrastText' 
                                  : 'text.primary',
                              }}
                            >
                              {child.icon}
                            </ListItemIcon>
                            <ListItemText primary={child.text} />
                          </ListItemButton>
                        </ListItem>
                      ))}
                    </List>
                  </Collapse>
                )}
              </React.Fragment>
            )
          }

          return (
            <ListItem key={item.text} disablePadding>
              <ListItemButton
                onClick={() => handleNavigation(item.path!)}
                sx={{
                  minHeight: 48,
                  justifyContent: open ? 'initial' : 'center',
                  px: 2.5,
                  borderRadius: 1,
                  mb: 0.5,
                  backgroundColor: isActive(item.path!) 
                    ? 'primary.main' 
                    : 'transparent',
                  color: isActive(item.path!) 
                    ? 'primary.contrastText' 
                    : 'text.primary',
                  '&:hover': {
                    backgroundColor: isActive(item.path!) 
                      ? 'primary.dark' 
                      : 'action.hover',
                  },
                }}
              >
                <ListItemIcon
                  sx={{
                    minWidth: 0,
                    mr: open ? 3 : 'auto',
                    justifyContent: 'center',
                    color: isActive(item.path!) 
                      ? 'primary.contrastText' 
                      : 'text.primary',
                  }}
                >
                  {item.icon}
                </ListItemIcon>
                {open && <ListItemText primary={item.text} />}
              </ListItemButton>
            </ListItem>
          )
        })}
      </List>
    </Box>
  )

  return (
    <Drawer
      variant={isMobile ? 'temporary' : 'persistent'}
      open={open}
      onClose={onClose}
      sx={{
        width: drawerWidth,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: drawerWidth,
          boxSizing: 'border-box',
          transition: (theme) =>
            theme.transitions.create('width', {
              easing: theme.transitions.easing.sharp,
              duration: theme.transitions.duration.enteringScreen,
            }),
        },
      }}
    >
      {drawerContent}
    </Drawer>
  )
}

export default Sidebar
