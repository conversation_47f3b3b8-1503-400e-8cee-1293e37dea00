from pydantic import BaseModel
from typing import Optional
from datetime import datetime
from decimal import Decimal
from app.models.invoice import InvoiceStatus

class InvoiceBase(BaseModel):
    customer_id: int
    subtotal: Decimal
    tax_rate: Decimal = Decimal('0')
    tax_amount: Decimal = Decimal('0')
    discount_amount: Decimal = Decimal('0')
    total_amount: Decimal
    issue_date: datetime
    due_date: datetime
    billing_period_start: datetime
    billing_period_end: datetime
    notes: Optional[str] = None
    terms_conditions: Optional[str] = None

class InvoiceCreate(InvoiceBase):
    pass

class InvoiceUpdate(BaseModel):
    customer_id: Optional[int] = None
    subtotal: Optional[Decimal] = None
    tax_rate: Optional[Decimal] = None
    tax_amount: Optional[Decimal] = None
    discount_amount: Optional[Decimal] = None
    total_amount: Optional[Decimal] = None
    status: Optional[InvoiceStatus] = None
    issue_date: Optional[datetime] = None
    due_date: Optional[datetime] = None
    paid_date: Optional[datetime] = None
    billing_period_start: Optional[datetime] = None
    billing_period_end: Optional[datetime] = None
    notes: Optional[str] = None
    terms_conditions: Optional[str] = None

class InvoiceResponse(InvoiceBase):
    id: int
    invoice_number: str
    status: InvoiceStatus
    paid_date: Optional[datetime] = None
    pdf_path: Optional[str] = None
    email_sent: bool = False
    email_sent_at: Optional[datetime] = None
    created_by: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    # Include customer details
    customer: Optional[dict] = None
    created_by_user: Optional[dict] = None

    class Config:
        from_attributes = True

class InvoiceSearch(BaseModel):
    customer_id: Optional[int] = None
    status: Optional[InvoiceStatus] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    billing_month: Optional[int] = None
    billing_year: Optional[int] = None
    page: int = 1
    limit: int = 10

class InvoiceGenerate(BaseModel):
    customer_id: int
    billing_period_start: datetime
    billing_period_end: datetime
    tax_rate: Optional[Decimal] = Decimal('0')
    discount_amount: Optional[Decimal] = Decimal('0')
    notes: Optional[str] = None
    terms_conditions: Optional[str] = None
