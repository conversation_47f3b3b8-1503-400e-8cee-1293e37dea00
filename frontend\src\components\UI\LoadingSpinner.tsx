import React from 'react'
import { CircularProgress } from '@mui/material'

interface LoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large'
  className?: string
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  size = 'medium', 
  className = '' 
}) => {
  const sizeMap = {
    small: 20,
    medium: 40,
    large: 60,
  }

  return (
    <div className={`flex items-center justify-center ${className}`}>
      <CircularProgress size={sizeMap[size]} />
    </div>
  )
}

export default LoadingSpinner
