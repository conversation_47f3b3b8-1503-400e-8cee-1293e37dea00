from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session, joinedload
from typing import List, Optional
from datetime import datetime, timedelta
from app.database import get_db
from app.models.invoice import Invoice, InvoiceStatus
from app.models.customer import Customer
from app.models.user import User
from app.schemas.invoice import InvoiceCreate, InvoiceUpdate, InvoiceResponse, InvoiceGenerate
from app.core.security import get_current_active_user

router = APIRouter()

def generate_invoice_number(db: Session) -> str:
    """Generate unique invoice number."""
    last_invoice = db.query(Invoice).order_by(Invoice.id.desc()).first()
    if last_invoice:
        last_id = int(last_invoice.invoice_number.replace("INV", ""))
        new_id = last_id + 1
    else:
        new_id = 1
    
    return f"INV{new_id:06d}"

@router.get("/", response_model=List[InvoiceResponse])
async def get_invoices(
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    customer_id: Optional[int] = None,
    status: Optional[InvoiceStatus] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get list of invoices with filtering."""
    query = db.query(Invoice).options(
        joinedload(Invoice.customer),
        joinedload(Invoice.created_by_user)
    )
    
    # Filters
    if customer_id:
        query = query.filter(Invoice.customer_id == customer_id)
    
    if status:
        query = query.filter(Invoice.status == status)
    
    if start_date:
        query = query.filter(Invoice.issue_date >= start_date)
    
    if end_date:
        query = query.filter(Invoice.issue_date <= end_date)
    
    invoices = query.order_by(Invoice.created_at.desc()).offset(skip).limit(limit).all()
    
    # Convert to response format with related data
    result = []
    for invoice in invoices:
        invoice_dict = InvoiceResponse.from_orm(invoice).dict()
        
        # Add customer details
        if invoice.customer:
            invoice_dict["customer"] = {
                "id": invoice.customer.id,
                "customer_id": invoice.customer.customer_id,
                "name": invoice.customer.name,
                "phone": invoice.customer.phone,
                "email": invoice.customer.email
            }
        
        # Add user details
        if invoice.created_by_user:
            invoice_dict["created_by_user"] = {
                "id": invoice.created_by_user.id,
                "full_name": invoice.created_by_user.full_name,
                "username": invoice.created_by_user.username
            }
        
        result.append(invoice_dict)
    
    return result

@router.post("/generate", response_model=InvoiceResponse)
async def generate_invoice(
    invoice_data: InvoiceGenerate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Generate invoice for a customer."""
    # Validate customer exists
    customer = db.query(Customer).filter(Customer.id == invoice_data.customer_id).first()
    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer not found"
        )
    
    # Calculate amounts based on customer's plan
    if not customer.plan:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Customer does not have an assigned plan"
        )
    
    subtotal = customer.plan.price
    tax_amount = subtotal * (invoice_data.tax_rate / 100) if invoice_data.tax_rate else 0
    total_amount = subtotal + tax_amount - (invoice_data.discount_amount or 0)
    
    # Generate invoice number
    invoice_number = generate_invoice_number(db)
    
    # Calculate due date (30 days from issue date)
    issue_date = datetime.utcnow()
    due_date = issue_date + timedelta(days=30)
    
    # Create invoice
    db_invoice = Invoice(
        invoice_number=invoice_number,
        customer_id=invoice_data.customer_id,
        subtotal=subtotal,
        tax_rate=invoice_data.tax_rate or 0,
        tax_amount=tax_amount,
        discount_amount=invoice_data.discount_amount or 0,
        total_amount=total_amount,
        issue_date=issue_date,
        due_date=due_date,
        billing_period_start=invoice_data.billing_period_start,
        billing_period_end=invoice_data.billing_period_end,
        notes=invoice_data.notes,
        terms_conditions=invoice_data.terms_conditions,
        created_by=current_user.id
    )
    
    db.add(db_invoice)
    db.commit()
    db.refresh(db_invoice)
    
    # Load related data for response
    invoice_response = InvoiceResponse.from_orm(db_invoice)
    
    # Add customer details
    invoice_response.customer = {
        "id": customer.id,
        "customer_id": customer.customer_id,
        "name": customer.name,
        "phone": customer.phone,
        "email": customer.email
    }
    
    # Add user details
    invoice_response.created_by_user = {
        "id": current_user.id,
        "full_name": current_user.full_name,
        "username": current_user.username
    }
    
    return invoice_response

@router.get("/{invoice_id}", response_model=InvoiceResponse)
async def get_invoice(
    invoice_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get invoice by ID."""
    invoice = db.query(Invoice).options(
        joinedload(Invoice.customer),
        joinedload(Invoice.created_by_user)
    ).filter(Invoice.id == invoice_id).first()
    
    if not invoice:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Invoice not found"
        )
    
    invoice_response = InvoiceResponse.from_orm(invoice)
    
    # Add related data
    if invoice.customer:
        invoice_response.customer = {
            "id": invoice.customer.id,
            "customer_id": invoice.customer.customer_id,
            "name": invoice.customer.name,
            "phone": invoice.customer.phone,
            "email": invoice.customer.email
        }
    
    if invoice.created_by_user:
        invoice_response.created_by_user = {
            "id": invoice.created_by_user.id,
            "full_name": invoice.created_by_user.full_name,
            "username": invoice.created_by_user.username
        }
    
    return invoice_response

@router.put("/{invoice_id}", response_model=InvoiceResponse)
async def update_invoice(
    invoice_id: int,
    invoice_data: InvoiceUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update invoice."""
    invoice = db.query(Invoice).filter(Invoice.id == invoice_id).first()
    if not invoice:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Invoice not found"
        )
    
    # Update invoice fields
    update_data = invoice_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(invoice, field, value)
    
    db.commit()
    db.refresh(invoice)
    
    return InvoiceResponse.from_orm(invoice)

@router.post("/{invoice_id}/mark-paid")
async def mark_invoice_paid(
    invoice_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Mark invoice as paid."""
    invoice = db.query(Invoice).filter(Invoice.id == invoice_id).first()
    if not invoice:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Invoice not found"
        )
    
    if invoice.status == InvoiceStatus.PAID:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invoice is already marked as paid"
        )
    
    # Update invoice status
    invoice.status = InvoiceStatus.PAID
    invoice.paid_date = datetime.utcnow()
    
    db.commit()
    
    return {"message": "Invoice marked as paid successfully"}

@router.post("/{invoice_id}/send-email")
async def send_invoice_email(
    invoice_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Send invoice via email."""
    invoice = db.query(Invoice).options(joinedload(Invoice.customer)).filter(Invoice.id == invoice_id).first()
    if not invoice:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Invoice not found"
        )
    
    if not invoice.customer.email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Customer does not have an email address"
        )
    
    # TODO: Implement email sending logic
    # For now, just mark as sent
    invoice.email_sent = True
    invoice.email_sent_at = datetime.utcnow()
    db.commit()
    
    return {"message": "Invoice sent via email successfully"}

@router.delete("/{invoice_id}")
async def delete_invoice(
    invoice_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete invoice."""
    invoice = db.query(Invoice).filter(Invoice.id == invoice_id).first()
    if not invoice:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Invoice not found"
        )
    
    db.delete(invoice)
    db.commit()
    
    return {"message": "Invoice deleted successfully"}
