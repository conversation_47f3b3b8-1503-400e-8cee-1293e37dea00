@echo off
REM Cable Operator Management System - Startup Script for Windows

echo 🚀 Starting Cable Operator Management System...

REM Check if Docker is installed
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not installed. Please install Docker Desktop first.
    pause
    exit /b 1
)

docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker Compose is not installed. Please install Docker Compose first.
    pause
    exit /b 1
)

REM Create environment files if they don't exist
if not exist backend\.env (
    echo 📝 Creating backend environment file...
    copy backend\.env.example backend\.env
    echo ✅ Backend .env created. Please edit it with your settings.
)

if not exist frontend\.env (
    echo 📝 Creating frontend environment file...
    copy frontend\.env.example frontend\.env
    echo ✅ Frontend .env created.
)

REM Start the application
echo 🐳 Starting Docker containers...
docker-compose up --build -d

echo ⏳ Waiting for services to start...
timeout /t 10 /nobreak >nul

REM Check if services are running
docker-compose ps | findstr "Up" >nul
if %errorlevel% equ 0 (
    echo ✅ Services started successfully!
    echo.
    echo 🌐 Application URLs:
    echo    Frontend: http://localhost:3000
    echo    Backend API: http://localhost:8000
    echo    API Documentation: http://localhost:8000/docs
    echo.
    echo 📊 To view logs:
    echo    docker-compose logs -f
    echo.
    echo 🛑 To stop the application:
    echo    docker-compose down
) else (
    echo ❌ Failed to start services. Check the logs:
    docker-compose logs
)

pause
