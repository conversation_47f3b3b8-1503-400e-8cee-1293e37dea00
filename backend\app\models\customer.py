from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, Enum, ForeignKey, Numeric
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from app.database import Base

class ConnectionType(str, enum.Enum):
    CABLE = "cable"
    INTERNET = "internet"
    BOTH = "both"

class CustomerStatus(str, enum.Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"

class Customer(Base):
    __tablename__ = "customers"

    id = Column(Integer, primary_key=True, index=True)
    customer_id = Column(String, unique=True, index=True, nullable=False)  # Custom ID like CUS001
    name = Column(String, nullable=False)
    email = Column(String, nullable=True)
    phone = Column(String, nullable=False)
    address = Column(Text, nullable=False)
    
    # Connection details
    connection_type = Column(Enum(ConnectionType), nullable=False)
    connection_date = Column(DateTime(timezone=True), nullable=False)
    status = Column(Enum(CustomerStatus), default=CustomerStatus.ACTIVE)
    
    # Plan assignment
    plan_id = Column(Integer, ForeignKey("plans.id"), nullable=True)
    
    # Additional details
    notes = Column(Text, nullable=True)
    installation_address = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    plan = relationship("Plan", back_populates="customers")
    payments = relationship("Payment", back_populates="customer")
    invoices = relationship("Invoice", back_populates="customer")

    def __repr__(self):
        return f"<Customer(id={self.id}, customer_id={self.customer_id}, name={self.name})>"
