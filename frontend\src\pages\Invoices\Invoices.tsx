import React from 'react'
import { Box, Typography, Button, Paper } from '@mui/material'
import { Add } from '@mui/icons-material'

const Invoices: React.FC = () => {
  return (
    <Box sx={{ flexGrow: 1 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" gutterBottom fontWeight="bold">
          Invoices
        </Typography>
        <Button variant="contained" startIcon={<Add />}>
          Generate Invoice
        </Button>
      </Box>

      <Paper sx={{ p: 4, textAlign: 'center' }}>
        <Typography variant="h6" color="text.secondary">
          Invoice management coming soon...
        </Typography>
      </Paper>
    </Box>
  )
}

export default Invoices
