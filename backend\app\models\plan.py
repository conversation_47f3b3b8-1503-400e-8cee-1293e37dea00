from sqlalchemy import Column, Integer, String, Text, Numeric, Boolean, DateTime, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from app.database import Base

class PlanType(str, enum.Enum):
    CABLE = "cable"
    INTERNET = "internet"
    COMBO = "combo"

class Plan(Base):
    __tablename__ = "plans"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    plan_type = Column(Enum(PlanType), nullable=False)
    description = Column(Text, nullable=True)
    
    # Internet plan details
    speed_mbps = Column(Integer, nullable=True)  # For internet plans
    data_limit_gb = Column(Integer, nullable=True)  # For internet plans
    
    # Cable plan details
    channels_count = Column(Integer, nullable=True)  # For cable plans
    hd_channels = Column(Integer, nullable=True)  # For cable plans
    
    # Pricing
    price = Column(Numeric(10, 2), nullable=False)
    setup_fee = Column(Numeric(10, 2), default=0)
    
    # Duration and billing
    duration_months = Column(Integer, default=1)  # Billing cycle
    
    # Status
    is_active = Column(Boolean, default=True)
    
    # Features
    features = Column(Text, nullable=True)  # JSON string of features
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    customers = relationship("Customer", back_populates="plan")

    def __repr__(self):
        return f"<Plan(id={self.id}, name={self.name}, type={self.plan_type}, price={self.price})>"
