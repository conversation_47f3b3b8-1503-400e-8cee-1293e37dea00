#!/usr/bin/env python3
"""
Test script to verify the setup is working correctly
"""

def test_imports():
    """Test if all required packages can be imported"""
    try:
        import fastapi
        print("✅ FastAPI imported successfully")
        
        import uvicorn
        print("✅ Uvicorn imported successfully")
        
        import sqlalchemy
        print("✅ SQLAlchemy imported successfully")
        
        import pymysql
        print("✅ PyMySQL imported successfully")
        
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_database_connection():
    """Test database connection"""
    try:
        import pymysql
        
        # Test connection
        connection = pymysql.connect(
            host='localhost',
            user='gst_user',
            password='admin@123',
            database='cable_software'
        )
        
        print("✅ MySQL connection successful!")
        
        # Test basic query
        cursor = connection.cursor()
        cursor.execute("SELECT 1")
        result = cursor.fetchone()
        print(f"✅ Database query successful: {result}")
        
        cursor.close()
        connection.close()
        
        return True
    except Exception as e:
        print(f"❌ Database connection error: {e}")
        return False

def test_fastapi_app():
    """Test if FastAPI app can be created"""
    try:
        from fastapi import FastAPI
        
        app = FastAPI()
        
        @app.get("/")
        def read_root():
            return {"message": "Hello World"}
        
        print("✅ FastAPI app created successfully")
        return True
    except Exception as e:
        print(f"❌ FastAPI app creation error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing Cable Management System Setup...")
    print("=" * 50)
    
    # Test imports
    print("\n📦 Testing Package Imports:")
    imports_ok = test_imports()
    
    # Test database
    print("\n🗄️ Testing Database Connection:")
    db_ok = test_database_connection()
    
    # Test FastAPI
    print("\n🌐 Testing FastAPI:")
    app_ok = test_fastapi_app()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Setup Test Summary:")
    print(f"   Imports: {'✅ PASS' if imports_ok else '❌ FAIL'}")
    print(f"   Database: {'✅ PASS' if db_ok else '❌ FAIL'}")
    print(f"   FastAPI: {'✅ PASS' if app_ok else '❌ FAIL'}")
    
    if imports_ok and db_ok and app_ok:
        print("\n🎉 All tests passed! System is ready to run.")
        print("\nNext steps:")
        print("1. Run: uvicorn main:app --reload --host 0.0.0.0 --port 8000")
        print("2. Open: http://localhost:8000/docs")
    else:
        print("\n⚠️ Some tests failed. Please fix the issues above.")
