from pydantic import BaseModel
from typing import Optional
from datetime import datetime
from decimal import Decimal
from app.models.plan import PlanType

class PlanBase(BaseModel):
    name: str
    plan_type: PlanType
    description: Optional[str] = None
    speed_mbps: Optional[int] = None
    data_limit_gb: Optional[int] = None
    channels_count: Optional[int] = None
    hd_channels: Optional[int] = None
    price: Decimal
    setup_fee: Decimal = Decimal('0')
    duration_months: int = 1
    features: Optional[str] = None

class PlanCreate(PlanBase):
    pass

class PlanUpdate(BaseModel):
    name: Optional[str] = None
    plan_type: Optional[PlanType] = None
    description: Optional[str] = None
    speed_mbps: Optional[int] = None
    data_limit_gb: Optional[int] = None
    channels_count: Optional[int] = None
    hd_channels: Optional[int] = None
    price: Optional[Decimal] = None
    setup_fee: Optional[Decimal] = None
    duration_months: Optional[int] = None
    features: Optional[str] = None
    is_active: Optional[bool] = None

class PlanResponse(PlanBase):
    id: int
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    # Customer count for this plan
    customer_count: Optional[int] = 0

    class Config:
        from_attributes = True

class PlanSearch(BaseModel):
    query: Optional[str] = None
    plan_type: Optional[PlanType] = None
    is_active: Optional[bool] = None
    min_price: Optional[Decimal] = None
    max_price: Optional[Decimal] = None
    page: int = 1
    limit: int = 10
