from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import or_, and_
from typing import List, Optional
from app.database import get_db
from app.models.customer import Customer, ConnectionType, CustomerStatus
from app.models.plan import Plan
from app.models.user import User
from app.schemas.customer import CustomerCreate, CustomerUpdate, CustomerResponse, CustomerSearch
from app.core.security import get_current_active_user

router = APIRouter()

def generate_customer_id(db: Session) -> str:
    """Generate unique customer ID."""
    last_customer = db.query(Customer).order_by(Customer.id.desc()).first()
    if last_customer:
        last_id = int(last_customer.customer_id.replace("CUS", ""))
        new_id = last_id + 1
    else:
        new_id = 1
    
    return f"CUS{new_id:04d}"

@router.get("/", response_model=List[CustomerResponse])
async def get_customers(
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    search: Optional[str] = None,
    connection_type: Optional[ConnectionType] = None,
    status: Optional[CustomerStatus] = None,
    plan_id: Optional[int] = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get list of customers with filtering and search."""
    query = db.query(Customer).options(joinedload(Customer.plan))
    
    # Search functionality
    if search:
        search_filter = or_(
            Customer.name.ilike(f"%{search}%"),
            Customer.customer_id.ilike(f"%{search}%"),
            Customer.phone.ilike(f"%{search}%"),
            Customer.email.ilike(f"%{search}%")
        )
        query = query.filter(search_filter)
    
    # Filters
    if connection_type:
        query = query.filter(Customer.connection_type == connection_type)
    
    if status:
        query = query.filter(Customer.status == status)
    
    if plan_id:
        query = query.filter(Customer.plan_id == plan_id)
    
    customers = query.offset(skip).limit(limit).all()
    
    # Convert to response format with plan details
    result = []
    for customer in customers:
        customer_dict = CustomerResponse.from_orm(customer).dict()
        if customer.plan:
            customer_dict["plan"] = {
                "id": customer.plan.id,
                "name": customer.plan.name,
                "plan_type": customer.plan.plan_type,
                "price": float(customer.plan.price)
            }
        result.append(customer_dict)
    
    return result

@router.post("/", response_model=CustomerResponse)
async def create_customer(
    customer_data: CustomerCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create a new customer."""
    # Check if phone number already exists
    existing_customer = db.query(Customer).filter(Customer.phone == customer_data.phone).first()
    if existing_customer:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Customer with this phone number already exists"
        )
    
    # Validate plan if provided
    if customer_data.plan_id:
        plan = db.query(Plan).filter(Plan.id == customer_data.plan_id).first()
        if not plan:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Plan not found"
            )
    
    # Generate customer ID
    customer_id = generate_customer_id(db)
    
    # Create customer
    db_customer = Customer(
        customer_id=customer_id,
        **customer_data.dict()
    )
    
    db.add(db_customer)
    db.commit()
    db.refresh(db_customer)
    
    # Load plan details for response
    db.refresh(db_customer)
    customer_response = CustomerResponse.from_orm(db_customer)
    
    if db_customer.plan:
        customer_response.plan = {
            "id": db_customer.plan.id,
            "name": db_customer.plan.name,
            "plan_type": db_customer.plan.plan_type,
            "price": float(db_customer.plan.price)
        }
    
    return customer_response

@router.get("/{customer_id}", response_model=CustomerResponse)
async def get_customer(
    customer_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get customer by ID."""
    customer = db.query(Customer).options(joinedload(Customer.plan)).filter(Customer.id == customer_id).first()
    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer not found"
        )
    
    customer_response = CustomerResponse.from_orm(customer)
    if customer.plan:
        customer_response.plan = {
            "id": customer.plan.id,
            "name": customer.plan.name,
            "plan_type": customer.plan.plan_type,
            "price": float(customer.plan.price)
        }
    
    return customer_response

@router.put("/{customer_id}", response_model=CustomerResponse)
async def update_customer(
    customer_id: int,
    customer_data: CustomerUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update customer."""
    customer = db.query(Customer).filter(Customer.id == customer_id).first()
    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer not found"
        )
    
    # Check if phone number already exists (excluding current customer)
    if customer_data.phone:
        existing_customer = db.query(Customer).filter(
            and_(Customer.phone == customer_data.phone, Customer.id != customer_id)
        ).first()
        if existing_customer:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Customer with this phone number already exists"
            )
    
    # Validate plan if provided
    if customer_data.plan_id:
        plan = db.query(Plan).filter(Plan.id == customer_data.plan_id).first()
        if not plan:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Plan not found"
            )
    
    # Update customer fields
    update_data = customer_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(customer, field, value)
    
    db.commit()
    db.refresh(customer)
    
    # Load plan details for response
    customer_response = CustomerResponse.from_orm(customer)
    if customer.plan:
        customer_response.plan = {
            "id": customer.plan.id,
            "name": customer.plan.name,
            "plan_type": customer.plan.plan_type,
            "price": float(customer.plan.price)
        }
    
    return customer_response

@router.delete("/{customer_id}")
async def delete_customer(
    customer_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete customer."""
    customer = db.query(Customer).filter(Customer.id == customer_id).first()
    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer not found"
        )
    
    db.delete(customer)
    db.commit()
    
    return {"message": "Customer deleted successfully"}
