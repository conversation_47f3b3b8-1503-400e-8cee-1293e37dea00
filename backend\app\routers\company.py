from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File
from sqlalchemy.orm import Session
from typing import Optional
import os
import uuid
from app.database import get_db
from app.models.company import Company
from app.models.user import User, UserRole
from app.schemas.company import CompanyCreate, CompanyUpdate, CompanyResponse
from app.core.security import get_current_active_user, require_role
from app.core.config import settings

router = APIRouter()

@router.post("/setup", response_model=CompanyResponse)
async def setup_company(
    company_data: CompanyCreate,
    current_user: User = Depends(require_role([UserRole.OWNER])),
    db: Session = Depends(get_db)
):
    """Setup company information (first time setup)."""
    # Check if company already exists
    existing_company = db.query(Company).first()
    if existing_company:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Company already setup"
        )
    
    # Create company
    db_company = Company(**company_data.dict())
    db.add(db_company)
    db.commit()
    db.refresh(db_company)
    
    return CompanyResponse.from_orm(db_company)

@router.get("/", response_model=CompanyResponse)
async def get_company(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get company information."""
    company = db.query(Company).first()
    if not company:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Company not found"
        )
    
    return CompanyResponse.from_orm(company)

@router.put("/", response_model=CompanyResponse)
async def update_company(
    company_data: CompanyUpdate,
    current_user: User = Depends(require_role([UserRole.OWNER, UserRole.ADMIN])),
    db: Session = Depends(get_db)
):
    """Update company information."""
    company = db.query(Company).first()
    if not company:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Company not found"
        )
    
    # Update company fields
    update_data = company_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(company, field, value)
    
    db.commit()
    db.refresh(company)
    
    return CompanyResponse.from_orm(company)

@router.post("/upload-logo")
async def upload_logo(
    file: UploadFile = File(...),
    current_user: User = Depends(require_role([UserRole.OWNER, UserRole.ADMIN])),
    db: Session = Depends(get_db)
):
    """Upload company logo."""
    # Validate file type
    allowed_types = ["image/jpeg", "image/png", "image/gif"]
    if file.content_type not in allowed_types:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid file type. Only JPEG, PNG, and GIF are allowed."
        )
    
    # Validate file size
    if file.size > settings.MAX_FILE_SIZE:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File too large"
        )
    
    # Generate unique filename
    file_extension = file.filename.split(".")[-1]
    filename = f"logo_{uuid.uuid4()}.{file_extension}"
    file_path = os.path.join(settings.UPLOAD_DIR, filename)
    
    # Save file
    with open(file_path, "wb") as buffer:
        content = await file.read()
        buffer.write(content)
    
    # Update company logo URL
    company = db.query(Company).first()
    if company:
        # Remove old logo file if exists
        if company.logo_url:
            old_file_path = os.path.join(settings.UPLOAD_DIR, os.path.basename(company.logo_url))
            if os.path.exists(old_file_path):
                os.remove(old_file_path)
        
        company.logo_url = f"/uploads/{filename}"
        db.commit()
    
    return {"message": "Logo uploaded successfully", "logo_url": f"/uploads/{filename}"}

@router.get("/check-setup")
async def check_setup(db: Session = Depends(get_db)):
    """Check if company setup is completed."""
    company = db.query(Company).first()
    return {"setup_completed": company is not None}
