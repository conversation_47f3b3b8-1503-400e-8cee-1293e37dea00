# Cable Operator Management System

A comprehensive, production-ready customer management system for cable and internet service providers.

## 🚀 Features

### Core Features
- **Customer Management**: Add, edit, delete, and search customers with connection details
- **Plan Management**: Manage internet and cable plans with pricing
- **Payment Tracking**: Track payments, generate invoices, and manage billing
- **Dashboard Analytics**: Revenue tracking, customer statistics, and visual charts
- **Multi-User Support**: Role-based access for admin and staff
- **Invoice Generation**: PDF invoices with email delivery
- **Dark Mode**: Modern UI with dark/light theme toggle

### Advanced Features
- **Company Setup**: First-time registration wizard for business details
- **Monthly Billing**: Automated recurring bill generation
- **Smart Search**: Advanced filtering and search across all modules
- **Data Export**: Export to Excel/CSV formats
- **Backup & Restore**: Database backup functionality
- **Mobile Responsive**: Works seamlessly on desktop and mobile

## 🛠️ Tech Stack

- **Frontend**: React.js + Vite + TailwindCSS + Material UI
- **Backend**: Python FastAPI
- **Database**: PostgreSQL
- **Authentication**: JWT with role-based access
- **Deployment**: Docker + Docker Compose

## 📦 Installation

### Prerequisites
- Python 3.8+
- Node.js 16+
- PostgreSQL 12+
- Docker (optional)

### Backend Setup

1. Activate virtual environment:
```bash
# Windows
cable_env\Scripts\activate

# Linux/Mac
source cable_env/bin/activate
```

2. Install dependencies:
```bash
cd backend
pip install -r requirements.txt
```

3. Set up database:
```bash
# Create PostgreSQL database
createdb cable_management

# Run migrations
alembic upgrade head
```

4. Start backend server:
```bash
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### Frontend Setup

1. Install dependencies:
```bash
cd frontend
npm install
```

2. Start development server:
```bash
npm run dev
```

### Docker Setup (Alternative)

```bash
docker-compose up --build
```

## 🔧 Configuration

### Environment Variables

Create `.env` files in both frontend and backend directories:

**Backend (.env)**:
```
DATABASE_URL=postgresql://username:password@localhost/cable_management
SECRET_KEY=your-secret-key-here
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
```

**Frontend (.env)**:
```
VITE_API_URL=http://localhost:8000
```

## 📱 Usage

1. **First Time Setup**: Register your company details
2. **User Management**: Create staff accounts with appropriate roles
3. **Customer Management**: Add customers with their connection details
4. **Plan Management**: Set up internet and cable plans
5. **Payment Tracking**: Record payments and generate invoices
6. **Analytics**: Monitor business performance through dashboard

## 🔐 Default Credentials

- **Admin**: <EMAIL> / admin123
- **Staff**: <EMAIL> / staff123

## 📊 API Documentation

Once the backend is running, visit:
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 🚀 Deployment

### Production Deployment

1. Build frontend:
```bash
cd frontend
npm run build
```

2. Configure production environment variables
3. Deploy using Docker:
```bash
docker-compose -f docker-compose.prod.yml up -d
```

## 📄 License

This project is licensed under the MIT License.

## 🤝 Support

For support and questions, please contact the development team.
