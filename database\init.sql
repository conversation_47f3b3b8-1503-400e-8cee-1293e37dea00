-- Initialize the database with basic setup
-- This file will be executed when the PostgreSQL container starts

-- Create database if it doesn't exist (handled by POSTGRES_DB env var)
-- CREATE DATABASE IF NOT EXISTS cable_management;

-- Grant privileges to the user
GRANT ALL PRIVILEGES ON DATABASE cable_management TO cable_user;

-- Create extensions if needed
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- You can add any initial data or additional setup here
-- For example, creating initial admin user, etc.

-- Note: The actual table creation will be handled by Alembic migrations
