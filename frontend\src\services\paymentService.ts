import api from './api'
import { Payment, PaymentFormData } from '../types'

export const paymentService = {
  async getPayments(params?: {
    skip?: number
    limit?: number
    customer_id?: number
    status?: string
    payment_method?: string
    billing_month?: number
    billing_year?: number
    start_date?: string
    end_date?: string
  }): Promise<Payment[]> {
    const response = await api.get('/api/payments', { params })
    return response.data
  },

  async getPayment(id: number): Promise<Payment> {
    const response = await api.get(`/api/payments/${id}`)
    return response.data
  },

  async createPayment(data: PaymentFormData): Promise<Payment> {
    const response = await api.post('/api/payments', data)
    return response.data
  },

  async updatePayment(id: number, data: Partial<PaymentFormData>): Promise<Payment> {
    const response = await api.put(`/api/payments/${id}`, data)
    return response.data
  },

  async markPaymentPaid(id: number, data?: {
    payment_date?: string
    reference_number?: string
    notes?: string
  }): Promise<void> {
    await api.post(`/api/payments/${id}/mark-paid`, data)
  },

  async deletePayment(id: number): Promise<void> {
    await api.delete(`/api/payments/${id}`)
  },
}
