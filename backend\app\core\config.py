from pydantic import BaseSettings
from typing import List

class Settings(BaseSettings):
    # Database (MySQL for production, SQLite for testing)
    # For MySQL: mysql+pymysql://cable_user:cable_password@localhost:3306/cable_management
    # For SQLite: sqlite:///./cable_management.db
    DATABASE_URL: str = "sqlite:///./cable_management.db"
    
    # Security
    SECRET_KEY: str = "your-super-secret-key-change-this-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # Email
    SMTP_SERVER: str = "smtp.gmail.com"
    SMTP_PORT: int = 587
    SMTP_USERNAME: str = ""
    SMTP_PASSWORD: str = ""
    SMTP_USE_TLS: bool = True
    
    # Application
    APP_NAME: str = "Cable Operator Management System"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = True
    
    # CORS
    ALLOWED_ORIGINS: List[str] = ["http://localhost:3000", "http://localhost:5173"]
    
    # File Upload
    MAX_FILE_SIZE: int = 10485760  # 10MB
    UPLOAD_DIR: str = "uploads"
    
    class Config:
        env_file = ".env"
        case_sensitive = True

settings = Settings()
