// User types
export interface User {
  id: number
  email: string
  username: string
  full_name: string
  role: 'owner' | 'admin' | 'staff'
  is_active: boolean
  phone?: string
  address?: string
  created_at: string
  updated_at?: string
  last_login?: string
}

export interface LoginCredentials {
  username: string
  password: string
}

export interface RegisterData {
  email: string
  username: string
  full_name: string
  password: string
  role?: 'owner' | 'admin' | 'staff'
  phone?: string
  address?: string
}

// Company types
export interface Company {
  id: number
  name: string
  owner_name: string
  email: string
  phone: string
  address?: string
  website?: string
  registration_number?: string
  tax_number?: string
  currency: string
  timezone: string
  logo_url?: string
  created_at: string
  updated_at?: string
}

// Customer types
export interface Customer {
  id: number
  customer_id: string
  name: string
  email?: string
  phone: string
  address: string
  connection_type: 'cable' | 'internet' | 'both'
  connection_date: string
  status: 'active' | 'inactive' | 'suspended'
  plan_id?: number
  plan?: {
    id: number
    name: string
    plan_type: string
    price: number
  }
  notes?: string
  installation_address?: string
  created_at: string
  updated_at?: string
}

// Plan types
export interface Plan {
  id: number
  name: string
  plan_type: 'cable' | 'internet' | 'combo'
  description?: string
  speed_mbps?: number
  data_limit_gb?: number
  channels_count?: number
  hd_channels?: number
  price: number
  setup_fee: number
  duration_months: number
  features?: string
  is_active: boolean
  customer_count?: number
  created_at: string
  updated_at?: string
}

// Payment types
export interface Payment {
  id: number
  payment_id: string
  customer_id: number
  plan_id?: number
  amount: number
  payment_method: 'cash' | 'card' | 'bank_transfer' | 'online' | 'cheque'
  status: 'paid' | 'pending' | 'overdue' | 'cancelled'
  billing_month: number
  billing_year: number
  due_date: string
  payment_date?: string
  notes?: string
  reference_number?: string
  created_by: number
  customer?: {
    id: number
    customer_id: string
    name: string
    phone: string
  }
  plan?: {
    id: number
    name: string
    plan_type: string
    price: number
  }
  created_by_user?: {
    id: number
    full_name: string
    username: string
  }
  created_at: string
  updated_at?: string
}

// Invoice types
export interface Invoice {
  id: number
  invoice_number: string
  customer_id: number
  subtotal: number
  tax_rate: number
  tax_amount: number
  discount_amount: number
  total_amount: number
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled'
  issue_date: string
  due_date: string
  paid_date?: string
  billing_period_start: string
  billing_period_end: string
  notes?: string
  terms_conditions?: string
  pdf_path?: string
  email_sent: boolean
  email_sent_at?: string
  created_by: number
  customer?: {
    id: number
    customer_id: string
    name: string
    phone: string
    email?: string
  }
  created_by_user?: {
    id: number
    full_name: string
    username: string
  }
  created_at: string
  updated_at?: string
}

// Dashboard types
export interface DashboardStats {
  customers: {
    total: number
    active: number
    inactive: number
    suspended: number
    new_this_month: number
  }
  payments: {
    monthly_revenue: number
    total_revenue: number
    overdue: number
    pending: number
  }
  plans: {
    distribution: Array<{
      name: string
      type: string
      customer_count: number
    }>
  }
}

export interface ChartData {
  chart_data: Array<{
    month: string
    revenue?: number
    new_customers?: number
    total_customers?: number
  }>
}

// API Response types
export interface ApiResponse<T> {
  data: T
  message?: string
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  limit: number
  pages: number
}

// Form types
export interface CustomerFormData {
  name: string
  email?: string
  phone: string
  address: string
  connection_type: 'cable' | 'internet' | 'both'
  connection_date: string
  status: 'active' | 'inactive' | 'suspended'
  plan_id?: number
  notes?: string
  installation_address?: string
}

export interface PlanFormData {
  name: string
  plan_type: 'cable' | 'internet' | 'combo'
  description?: string
  speed_mbps?: number
  data_limit_gb?: number
  channels_count?: number
  hd_channels?: number
  price: number
  setup_fee: number
  duration_months: number
  features?: string
}

export interface PaymentFormData {
  customer_id: number
  plan_id?: number
  amount: number
  payment_method: 'cash' | 'card' | 'bank_transfer' | 'online' | 'cheque'
  billing_month: number
  billing_year: number
  due_date: string
  notes?: string
  reference_number?: string
}
