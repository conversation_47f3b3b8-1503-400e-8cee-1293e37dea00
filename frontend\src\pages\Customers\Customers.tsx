import React, { useState } from 'react'
import {
  Box,
  Typography,
  Button,
  Paper,
  TextField,
  InputAdornment,
  Chip,
  IconButton,
  Menu,
  MenuItem,
} from '@mui/material'
import {
  Add,
  Search,
  FilterList,
  Edit,
  Delete,
  MoreVert,
} from '@mui/icons-material'
import { DataGrid, GridColDef, GridActionsCellItem } from '@mui/x-data-grid'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import { customerService } from '../../services/customerService'
import { Customer } from '../../types'
import { toast } from 'react-toastify'
import LoadingSpinner from '../../components/UI/LoadingSpinner'

const Customers: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [filterAnchorEl, setFilterAnchorEl] = useState<null | HTMLElement>(null)
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [connectionTypeFilter, setConnectionTypeFilter] = useState<string>('')
  
  const queryClient = useQueryClient()

  const { data: customers = [], isLoading } = useQuery(
    ['customers', searchTerm, statusFilter, connectionTypeFilter],
    () => customerService.getCustomers({
      search: searchTerm || undefined,
      status: statusFilter || undefined,
      connection_type: connectionTypeFilter || undefined,
    })
  )

  const deleteMutation = useMutation(customerService.deleteCustomer, {
    onSuccess: () => {
      queryClient.invalidateQueries('customers')
      toast.success('Customer deleted successfully')
    },
    onError: () => {
      toast.error('Failed to delete customer')
    },
  })

  const handleDelete = (id: number) => {
    if (window.confirm('Are you sure you want to delete this customer?')) {
      deleteMutation.mutate(id)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success'
      case 'inactive':
        return 'default'
      case 'suspended':
        return 'error'
      default:
        return 'default'
    }
  }

  const getConnectionTypeColor = (type: string) => {
    switch (type) {
      case 'cable':
        return 'primary'
      case 'internet':
        return 'secondary'
      case 'both':
        return 'info'
      default:
        return 'default'
    }
  }

  const columns: GridColDef[] = [
    {
      field: 'customer_id',
      headerName: 'Customer ID',
      width: 120,
      fontWeight: 'bold',
    },
    {
      field: 'name',
      headerName: 'Name',
      width: 200,
      flex: 1,
    },
    {
      field: 'phone',
      headerName: 'Phone',
      width: 150,
    },
    {
      field: 'connection_type',
      headerName: 'Connection',
      width: 120,
      renderCell: (params) => (
        <Chip
          label={params.value}
          color={getConnectionTypeColor(params.value) as any}
          size="small"
          variant="outlined"
        />
      ),
    },
    {
      field: 'status',
      headerName: 'Status',
      width: 100,
      renderCell: (params) => (
        <Chip
          label={params.value}
          color={getStatusColor(params.value) as any}
          size="small"
        />
      ),
    },
    {
      field: 'plan',
      headerName: 'Plan',
      width: 150,
      renderCell: (params) => (
        params.row.plan ? (
          <Chip
            label={params.row.plan.name}
            size="small"
            variant="outlined"
          />
        ) : (
          <Typography variant="body2" color="text.secondary">
            No plan
          </Typography>
        )
      ),
    },
    {
      field: 'connection_date',
      headerName: 'Connection Date',
      width: 150,
      renderCell: (params) => (
        new Date(params.value).toLocaleDateString()
      ),
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'Actions',
      width: 100,
      getActions: (params) => [
        <GridActionsCellItem
          icon={<Edit />}
          label="Edit"
          onClick={() => {
            // TODO: Open edit dialog
            console.log('Edit customer', params.id)
          }}
        />,
        <GridActionsCellItem
          icon={<Delete />}
          label="Delete"
          onClick={() => handleDelete(params.id as number)}
        />,
      ],
    },
  ]

  if (isLoading) {
    return <LoadingSpinner size="large" className="min-h-screen" />
  }

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Box sx={{ display: 'flex', justifyContent: 'between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" gutterBottom fontWeight="bold">
          Customers
        </Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => {
            // TODO: Open add customer dialog
            console.log('Add customer')
          }}
        >
          Add Customer
        </Button>
      </Box>

      {/* Search and Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <TextField
            placeholder="Search customers..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
            }}
            sx={{ flexGrow: 1 }}
          />
          
          <IconButton
            onClick={(e) => setFilterAnchorEl(e.currentTarget)}
          >
            <FilterList />
          </IconButton>
          
          <Menu
            anchorEl={filterAnchorEl}
            open={Boolean(filterAnchorEl)}
            onClose={() => setFilterAnchorEl(null)}
          >
            <MenuItem onClick={() => { setStatusFilter(''); setFilterAnchorEl(null) }}>
              All Status
            </MenuItem>
            <MenuItem onClick={() => { setStatusFilter('active'); setFilterAnchorEl(null) }}>
              Active
            </MenuItem>
            <MenuItem onClick={() => { setStatusFilter('inactive'); setFilterAnchorEl(null) }}>
              Inactive
            </MenuItem>
            <MenuItem onClick={() => { setStatusFilter('suspended'); setFilterAnchorEl(null) }}>
              Suspended
            </MenuItem>
          </Menu>

          {statusFilter && (
            <Chip
              label={`Status: ${statusFilter}`}
              onDelete={() => setStatusFilter('')}
              size="small"
            />
          )}
          
          {connectionTypeFilter && (
            <Chip
              label={`Type: ${connectionTypeFilter}`}
              onDelete={() => setConnectionTypeFilter('')}
              size="small"
            />
          )}
        </Box>
      </Paper>

      {/* Data Grid */}
      <Paper sx={{ height: 600, width: '100%' }}>
        <DataGrid
          rows={customers}
          columns={columns}
          pageSize={10}
          rowsPerPageOptions={[10, 25, 50]}
          disableSelectionOnClick
          sx={{
            border: 0,
            '& .MuiDataGrid-cell:hover': {
              color: 'primary.main',
            },
          }}
        />
      </Paper>
    </Box>
  )
}

export default Customers
