from pydantic import BaseModel
from typing import Optional
from datetime import datetime
from decimal import Decimal
from app.models.payment import PaymentStatus, PaymentMethod

class PaymentBase(BaseModel):
    customer_id: int
    plan_id: Optional[int] = None
    amount: Decimal
    payment_method: PaymentMethod
    billing_month: int
    billing_year: int
    due_date: datetime
    notes: Optional[str] = None
    reference_number: Optional[str] = None

class PaymentCreate(PaymentBase):
    pass

class PaymentUpdate(BaseModel):
    customer_id: Optional[int] = None
    plan_id: Optional[int] = None
    amount: Optional[Decimal] = None
    payment_method: Optional[PaymentMethod] = None
    status: Optional[PaymentStatus] = None
    billing_month: Optional[int] = None
    billing_year: Optional[int] = None
    due_date: Optional[datetime] = None
    payment_date: Optional[datetime] = None
    notes: Optional[str] = None
    reference_number: Optional[str] = None

class PaymentResponse(PaymentBase):
    id: int
    payment_id: str
    status: PaymentStatus
    payment_date: Optional[datetime] = None
    created_by: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    # Include customer and plan details
    customer: Optional[dict] = None
    plan: Optional[dict] = None
    created_by_user: Optional[dict] = None

    class Config:
        from_attributes = True

class PaymentSearch(BaseModel):
    customer_id: Optional[int] = None
    status: Optional[PaymentStatus] = None
    payment_method: Optional[PaymentMethod] = None
    billing_month: Optional[int] = None
    billing_year: Optional[int] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    page: int = 1
    limit: int = 10

class PaymentMarkPaid(BaseModel):
    payment_date: Optional[datetime] = None
    reference_number: Optional[str] = None
    notes: Optional[str] = None
