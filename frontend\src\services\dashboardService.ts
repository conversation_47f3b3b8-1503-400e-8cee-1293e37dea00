import api from './api'
import { DashboardStats, ChartData } from '../types'

export const dashboardService = {
  async getStats(): Promise<DashboardStats> {
    const response = await api.get('/api/dashboard/stats')
    return response.data
  },

  async getRevenueChart(months: number = 12): Promise<ChartData> {
    const response = await api.get('/api/dashboard/revenue-chart', {
      params: { months }
    })
    return response.data
  },

  async getCustomerGrowth(months: number = 12): Promise<ChartData> {
    const response = await api.get('/api/dashboard/customer-growth', {
      params: { months }
    })
    return response.data
  },

  async getRecentActivities(limit: number = 10) {
    const response = await api.get('/api/dashboard/recent-activities', {
      params: { limit }
    })
    return response.data
  },

  async getPaymentStatusSummary() {
    const response = await api.get('/api/dashboard/payment-status-summary')
    return response.data
  },

  async getTopPlans(limit: number = 5) {
    const response = await api.get('/api/dashboard/top-plans', {
      params: { limit }
    })
    return response.data
  },
}
