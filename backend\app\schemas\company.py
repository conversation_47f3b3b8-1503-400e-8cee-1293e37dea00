from pydantic import BaseModel, EmailStr
from typing import Optional
from datetime import datetime

class CompanyBase(BaseModel):
    name: str
    owner_name: str
    email: EmailStr
    phone: str
    address: Optional[str] = None
    website: Optional[str] = None
    registration_number: Optional[str] = None
    tax_number: Optional[str] = None
    currency: str = "USD"
    timezone: str = "UTC"

class CompanyCreate(CompanyBase):
    pass

class CompanyUpdate(BaseModel):
    name: Optional[str] = None
    owner_name: Optional[str] = None
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    address: Optional[str] = None
    website: Optional[str] = None
    registration_number: Optional[str] = None
    tax_number: Optional[str] = None
    currency: Optional[str] = None
    timezone: Optional[str] = None
    logo_url: Optional[str] = None

class CompanyResponse(CompanyBase):
    id: int
    logo_url: Optional[str] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
