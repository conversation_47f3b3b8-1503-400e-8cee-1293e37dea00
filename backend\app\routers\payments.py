from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_
from typing import List, Optional
from datetime import datetime
from app.database import get_db
from app.models.payment import Payment, PaymentStatus, PaymentMethod
from app.models.customer import Customer
from app.models.plan import Plan
from app.models.user import User
from app.schemas.payment import PaymentCreate, PaymentUpdate, PaymentResponse, PaymentSearch, PaymentMarkPaid
from app.core.security import get_current_active_user

router = APIRouter()

def generate_payment_id(db: Session) -> str:
    """Generate unique payment ID."""
    last_payment = db.query(Payment).order_by(Payment.id.desc()).first()
    if last_payment:
        last_id = int(last_payment.payment_id.replace("PAY", ""))
        new_id = last_id + 1
    else:
        new_id = 1
    
    return f"PAY{new_id:06d}"

@router.get("/", response_model=List[PaymentResponse])
async def get_payments(
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    customer_id: Optional[int] = None,
    status: Optional[PaymentStatus] = None,
    payment_method: Optional[PaymentMethod] = None,
    billing_month: Optional[int] = None,
    billing_year: Optional[int] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get list of payments with filtering."""
    query = db.query(Payment).options(
        joinedload(Payment.customer),
        joinedload(Payment.plan),
        joinedload(Payment.created_by_user)
    )
    
    # Filters
    if customer_id:
        query = query.filter(Payment.customer_id == customer_id)
    
    if status:
        query = query.filter(Payment.status == status)
    
    if payment_method:
        query = query.filter(Payment.payment_method == payment_method)
    
    if billing_month:
        query = query.filter(Payment.billing_month == billing_month)
    
    if billing_year:
        query = query.filter(Payment.billing_year == billing_year)
    
    if start_date:
        query = query.filter(Payment.due_date >= start_date)
    
    if end_date:
        query = query.filter(Payment.due_date <= end_date)
    
    payments = query.order_by(Payment.created_at.desc()).offset(skip).limit(limit).all()
    
    # Convert to response format with related data
    result = []
    for payment in payments:
        payment_dict = PaymentResponse.from_orm(payment).dict()
        
        # Add customer details
        if payment.customer:
            payment_dict["customer"] = {
                "id": payment.customer.id,
                "customer_id": payment.customer.customer_id,
                "name": payment.customer.name,
                "phone": payment.customer.phone
            }
        
        # Add plan details
        if payment.plan:
            payment_dict["plan"] = {
                "id": payment.plan.id,
                "name": payment.plan.name,
                "plan_type": payment.plan.plan_type,
                "price": float(payment.plan.price)
            }
        
        # Add user details
        if payment.created_by_user:
            payment_dict["created_by_user"] = {
                "id": payment.created_by_user.id,
                "full_name": payment.created_by_user.full_name,
                "username": payment.created_by_user.username
            }
        
        result.append(payment_dict)
    
    return result

@router.post("/", response_model=PaymentResponse)
async def create_payment(
    payment_data: PaymentCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create a new payment record."""
    # Validate customer exists
    customer = db.query(Customer).filter(Customer.id == payment_data.customer_id).first()
    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer not found"
        )
    
    # Validate plan if provided
    if payment_data.plan_id:
        plan = db.query(Plan).filter(Plan.id == payment_data.plan_id).first()
        if not plan:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Plan not found"
            )
    
    # Check if payment already exists for this customer and billing period
    existing_payment = db.query(Payment).filter(
        and_(
            Payment.customer_id == payment_data.customer_id,
            Payment.billing_month == payment_data.billing_month,
            Payment.billing_year == payment_data.billing_year
        )
    ).first()
    
    if existing_payment:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Payment already exists for this billing period"
        )
    
    # Generate payment ID
    payment_id = generate_payment_id(db)
    
    # Create payment
    db_payment = Payment(
        payment_id=payment_id,
        created_by=current_user.id,
        **payment_data.dict()
    )
    
    db.add(db_payment)
    db.commit()
    db.refresh(db_payment)
    
    # Load related data for response
    payment_response = PaymentResponse.from_orm(db_payment)
    
    # Add customer details
    payment_response.customer = {
        "id": customer.id,
        "customer_id": customer.customer_id,
        "name": customer.name,
        "phone": customer.phone
    }
    
    # Add plan details if exists
    if db_payment.plan:
        payment_response.plan = {
            "id": db_payment.plan.id,
            "name": db_payment.plan.name,
            "plan_type": db_payment.plan.plan_type,
            "price": float(db_payment.plan.price)
        }
    
    # Add user details
    payment_response.created_by_user = {
        "id": current_user.id,
        "full_name": current_user.full_name,
        "username": current_user.username
    }
    
    return payment_response

@router.get("/{payment_id}", response_model=PaymentResponse)
async def get_payment(
    payment_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get payment by ID."""
    payment = db.query(Payment).options(
        joinedload(Payment.customer),
        joinedload(Payment.plan),
        joinedload(Payment.created_by_user)
    ).filter(Payment.id == payment_id).first()
    
    if not payment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Payment not found"
        )
    
    payment_response = PaymentResponse.from_orm(payment)
    
    # Add related data
    if payment.customer:
        payment_response.customer = {
            "id": payment.customer.id,
            "customer_id": payment.customer.customer_id,
            "name": payment.customer.name,
            "phone": payment.customer.phone
        }
    
    if payment.plan:
        payment_response.plan = {
            "id": payment.plan.id,
            "name": payment.plan.name,
            "plan_type": payment.plan.plan_type,
            "price": float(payment.plan.price)
        }
    
    if payment.created_by_user:
        payment_response.created_by_user = {
            "id": payment.created_by_user.id,
            "full_name": payment.created_by_user.full_name,
            "username": payment.created_by_user.username
        }
    
    return payment_response

@router.put("/{payment_id}", response_model=PaymentResponse)
async def update_payment(
    payment_id: int,
    payment_data: PaymentUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update payment."""
    payment = db.query(Payment).filter(Payment.id == payment_id).first()
    if not payment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Payment not found"
        )
    
    # Update payment fields
    update_data = payment_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(payment, field, value)
    
    db.commit()
    db.refresh(payment)
    
    return PaymentResponse.from_orm(payment)

@router.post("/{payment_id}/mark-paid")
async def mark_payment_paid(
    payment_id: int,
    payment_data: PaymentMarkPaid,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Mark payment as paid."""
    payment = db.query(Payment).filter(Payment.id == payment_id).first()
    if not payment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Payment not found"
        )
    
    if payment.status == PaymentStatus.PAID:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Payment is already marked as paid"
        )
    
    # Update payment status
    payment.status = PaymentStatus.PAID
    payment.payment_date = payment_data.payment_date or datetime.utcnow()
    
    if payment_data.reference_number:
        payment.reference_number = payment_data.reference_number
    
    if payment_data.notes:
        payment.notes = payment_data.notes
    
    db.commit()
    
    return {"message": "Payment marked as paid successfully"}

@router.delete("/{payment_id}")
async def delete_payment(
    payment_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete payment."""
    payment = db.query(Payment).filter(Payment.id == payment_id).first()
    if not payment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Payment not found"
        )
    
    db.delete(payment)
    db.commit()
    
    return {"message": "Payment deleted successfully"}
